
Here are a few projects that are built on detectron2.
They are examples of how to use detectron2 as a library, to make your projects more
maintainable.

## Projects by Facebook

Note that these are research projects, and therefore may not have the same level
of support or stability of detectron2.

+ [DensePose: Dense Human Pose Estimation In The Wild](DensePose)
+ [Scale-Aware Trident Networks for Object Detection](TridentNet)
+ [TensorMask: A Foundation for Dense Object Segmentation](TensorMask)
+ [Mesh R-CNN](https://github.com/facebookresearch/meshrcnn)
+ [PointRend: Image Segmentation as Rendering](PointRend)
+ [Momentum Contrast for Unsupervised Visual Representation Learning](https://github.com/facebookresearch/moco/tree/master/detection)


## External Projects

External projects in the community that use detectron2:

<!--
 - If you want to contribute, note that:
 -  1. please add your project to the end of the list and try to use only one line
 -  2. the project must provide models trained on standard data
 -->

+ [VoVNet backbones](https://github.com/youngwanLEE/vovnet-detectron2).
+ [AdelaiDet](https://github.com/aim-uofa/adet), a detection toolbox from the Universtiy of Adelaide.
+ [CenterMask : Real-Time Anchor-Free Instance Segmentation](https://github.com/youngwanLEE/centermask2)
