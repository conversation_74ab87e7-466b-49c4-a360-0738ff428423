_BASE_: Base-PointRend-RCNN-FPN.yaml
MODEL:
  WEIGHTS: detectron2://ImageNetPretrained/MSRA/R-50.pkl
  MASK_ON: true
  RESNETS:
    DEPTH: 50
  ROI_HEADS:
    NUM_CLASSES: 1
  POINT_HEAD:
    NUM_CLASSES: 1
SOLVER:
  STEPS: (210000, 250000)
  MAX_ITER: 270000
  IMS_PER_BATCH: 1
# To add COCO AP evaluation against the higher-quality LVIS annotations.
# DATASETS:
#   TEST: ("coco_2017_val", "lvis_v0.5_val_cocofied")
DATASETS:
  TRAIN: ("CIHP_train",)
  TEST: ("CIHP_val",)
